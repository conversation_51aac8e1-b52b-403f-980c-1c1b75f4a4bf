// Dark color mode variables
//
// Custom variables for the `[data-bs-theme="dark"]` theme. Use this as a starting point for your own custom color modes by creating a new theme-specific file like `_variables-dark.scss` and adding the variables you need.

//
// Global colors
//

// scss-docs-start sass-dark-mode-vars
// scss-docs-start theme-text-dark-variables
$primary-text-emphasis-dark: tint-color($primary, 40%) !default;
$secondary-text-emphasis-dark: tint-color($secondary, 40%) !default;
$success-text-emphasis-dark: tint-color($success, 40%) !default;
$info-text-emphasis-dark: tint-color($info, 40%) !default;
$warning-text-emphasis-dark: tint-color($warning, 40%) !default;
$danger-text-emphasis-dark: tint-color($danger, 40%) !default;
$light-text-emphasis-dark: $gray-100 !default;
$dark-text-emphasis-dark: $gray-300 !default;
// scss-docs-end theme-text-dark-variables

// scss-docs-start theme-bg-subtle-dark-variables
$primary-bg-subtle-dark: shade-color($primary, 80%) !default;
$secondary-bg-subtle-dark: shade-color($secondary, 80%) !default;
$success-bg-subtle-dark: shade-color($success, 80%) !default;
$info-bg-subtle-dark: shade-color($info, 80%) !default;
$warning-bg-subtle-dark: shade-color($warning, 80%) !default;
$danger-bg-subtle-dark: shade-color($danger, 80%) !default;
$light-bg-subtle-dark: $gray-800 !default;
$dark-bg-subtle-dark: mix($gray-800, $black) !default;
// scss-docs-end theme-bg-subtle-dark-variables

// scss-docs-start theme-border-subtle-dark-variables
$primary-border-subtle-dark: shade-color($primary, 40%) !default;
$secondary-border-subtle-dark: shade-color($secondary, 40%) !default;
$success-border-subtle-dark: shade-color($success, 40%) !default;
$info-border-subtle-dark: shade-color($info, 40%) !default;
$warning-border-subtle-dark: shade-color($warning, 40%) !default;
$danger-border-subtle-dark: shade-color($danger, 40%) !default;
$light-border-subtle-dark: $gray-700 !default;
$dark-border-subtle-dark: $gray-800 !default;
// scss-docs-end theme-border-subtle-dark-variables

$body-color-dark: $gray-500 !default;
$body-bg-dark: $gray-900 !default;
$body-secondary-color-dark: rgba($body-color-dark, .50) !default;
$body-secondary-bg-dark: $gray-800 !default;
$body-tertiary-color-dark: rgba($body-color-dark, .5) !default;
$body-tertiary-bg-dark: mix($gray-800, $gray-900, 50%) !default;
$body-emphasis-color-dark: $white !default;
$border-color-dark: $gray-700 !default;
$border-color-translucent-dark: rgba($white, .15) !default;
$headings-color-dark: inherit !default;
$link-color-dark: tint-color($primary, 40%) !default;
$link-hover-color-dark: shift-color($link-color-dark, -$link-shade-percentage) !default;
$code-color-dark: tint-color($code-color, 40%) !default;


//
// Forms
//

$form-select-indicator-color-dark: $body-color-dark !default;
$form-select-indicator-dark: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color-dark}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>") !default;

$form-switch-color-dark: rgba($white, .25) !default;
$form-switch-bg-image-dark: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color-dark}'/></svg>") !default;

// scss-docs-start form-validation-colors-dark
$form-valid-color-dark: $green-300 !default;
$form-valid-border-color-dark: $green-300 !default;
$form-invalid-color-dark: $red-300 !default;
$form-invalid-border-color-dark: $red-300 !default;
// scss-docs-end form-validation-colors-dark


//
// Accordion
//
$accordion-icon-color-dark: $primary-text-emphasis-dark !default;
$accordion-icon-active-color-dark: $primary-text-emphasis-dark !default;

$accordion-button-icon-dark: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color-dark}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>") !default;
$accordion-button-active-icon-dark: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color-dark}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>") !default;
// scss-docs-end sass-dark-mode-vars


// ###############
// DARK MODE THEME
// ###############
[data-bs-theme="dark"] {

    h2, .h2, h3, .h3, a, p {
        color: $gray-200;
    }

    input,
    select,
    textarea {
        background-color: $gray-900 !important;
        color: $gray-100;
    }

    .text {
        blockquote {
            &:before {
                color: $primary;
            }
        }
    }

    .pill-sun {
        background-color: $gray-800;
        color: $gray-200;

        &:hover {
            background-color: $gray-700;
            color: $gray-100 !important;
        }

        &.active {
            color: $gray-100 !important;
        }
    }

    .pill-sun-large {
        background-color: $gray-800 !important;
        color: $gray-100 !important;

        &:hover {
            background-color: $gray-700 !important;
            color: $gray-200 !important;
        }

        &.active {
            color: $gray-100 !important;
            background-color: $secondary !important;
            border-color: $secondary !important;
        }
    }

    .rounded-pill:not(.rounded-pill-default) {
        background-color: $gray-800;
    }

    // Cookie Modal - Klaro //////////
    .cm-klaro {
        background-color: $gray-800 !important;

        h1, p, .cm-list-title {
            color: $gray-100 !important;
        }

        .slider.round {
            &::before {
                background-color: $gray-800 !important;
            }
        }

        .cm-btn-info {
            color: $gray-800 !important;
        }
    }

    // Flatpickr
    .flatpickr-months .flatpickr-month,
    .flatpickr-weekdays,
    .flatpickr-weekday,
    .flatpickr-weekdaycontainer,
    .flatpickr-time-separator {
        background-color: $gray-900;
        color: $gray-100;
    }

    .flatpickr-months .flatpickr-prev-month svg path,
    .flatpickr-months .flatpickr-next-month svg path {
        fill: $gray-100;
    }

    .cur-year,
    .arrowUp,
    .arrowDown {
        opacity: 1;
        color: $gray-100 !important;
    }

    .modal-dedication {
        .dedication {
            background-color: $gray-900 !important;
        }

        .modal-body {
            .dedication-song {
                background-color: $gray-900 !important;
                color: $gray-100;
            }
        }
    }

    .flatpickr-innerContainer {
        background-color: $gray-800;

        .flatpickr-day {
            color: $gray-100;

            &.flatpickr-disabled {
                color: $gray-600;
            }

            &:hover {
                color: $gray-800;
            }
        }

    }

    // Forms //////////
    #type-selector,
    #search-form-input,
    #search-form-separator,
    #datetime-selector {
        background-color: $gray-800;
    }

    #datetime-selector-container {
        input {
            background-color: $gray-800 !important;
        }
    }

    #search-form-input {
        & > input {
            background-color: $gray-800 !important;
            color: $gray-100;
        }
    }

    // Nav //////////
    #app-nav-logo, #weblinks-container {
        path {
            fill: $gray-100;
        }
    }

    #nav-desktop {
        #playlists-container {
            a.text-secondary {
                color: $yellow !important;
            }
        }
    }

    #nav-radio-universe-selector-dropdown, #dropdown-useful-links {
        background-color: $gray-800;
    }

    #app-nav-tabs {
        .btn {
            &.btn-nav {
                background-color: $gray-800;
                border-color: $gray-800;
                color: $gray-200;

                &.active {
                    background-color: $secondary;
                    border-color: $secondary;
                }

                &:hover {
                    &:not(.active) {
                        color: $gray-200;
                    }
                }
            }
        }

        span {
            color: $gray-200 !important;
        }
    }

    // Profile //////////
    #profile-picture-component {
        background-color: $gray-800;
    }

    #profile-picture-component,
    .profile-card,
    .choose-default-radio-station-card {
        background-color: $gray-800;
        color: $gray-200;
    }

    .profile-card {
        & > button > .pill-sun {
            background-color: $gray-900;

            &:hover {
                background-color: $gray-700;
            }
        }
    }

    // Footer //////////
    #footer-app-desktop {
        background-color: $gray-800;
    }

    #audio-playing-progress, #audio-player-volume-dropup, #audio-player-volume {
        background-color: $gray-800!important;
    }

    #audio-playing-progress, #audio-player-volume {
        &::-moz-range-track {
            background: linear-gradient(to right, $gray-400, $gray-400 var(--progress-value, 0%), $gray-600 var(--progress-value, 0%), $gray-600) !important;
        }

        &::-webkit-slider-runnable-track {
            background: linear-gradient(to right, $gray-400, $gray-400 var(--progress-value, 0%), $gray-600 var(--progress-value, 0%), $gray-600) !important;
        }

        &::-moz-range-thumb {
            background-color: $gray-300 !important;
        }

        &::-webkit-slider-thumb {
            background-color: $gray-300 !important;
        }
    }

    // Misc //////////
    .browse-index-component {
        color: $gray-100;

        h2 {
            color: $gray-100;
        }
    }

    .dropdown-item {
        &.active {
            color: $gray-100;
            background-color: darken($light, 75%);
        }

        &:hover {
            color: white;
            background-color: darken($light, 75%);
        }
    }

    .album-card-component {
        background-color: $gray-800;

        &.active, &:hover {
            background-color: $gray-700!important;
        }

        &.active {
            .album-title {
                color: $yellow!important;
            }
        }
    }

    .modal-content {
        background-color: $gray-800;
    }


    .choose-default-radio-station-card {
        background-color: $gray-600!important;
        color: $gray-200!important;
    }

    .event-card-component {
        background-color: $gray-800;
        color: $gray-100;

        &.active, &:hover {
            background-color: $gray-700;

            & > .event-card-body {
                & > .event-card-published-at {
                    & > span {
                        background-color: $gray-600 !important;;
                    }
                }
            }
        }

        &.active {
            .h4 {
                color: $yellow !important;
            }
        }

        & > .event-card-body {
            & > .event-card-published-at {
                & > span {
                    background-color: $gray-700 !important;
                    color: white !important;
                }
            }
        }

        h3 {
            color: $gray-100 !important;
        }
    }

    .song-card-component {
        &.active {
            .song-title, .song-title a {
                color: $yellow;
                @include transition();
            }
        }
    }

    .offcanvas-body {
        background-color: $gray-800;
    }

    .performer-card-component {
        color: $gray-100;
        background-color: $gray-800;

        &.active, &:hover {
            background-color: $gray-700 !important;
        }
    }

    .program-card-component {
        color: $gray-100;
        background-color: $gray-800;

        &.active, &:hover {
            background-color: $gray-700 !important;
        }
    }

    .podcast-card-component {
        background-color: $gray-800;

        &.active, &:hover {
            background-color: $gray-700 !important;
        }

        &.active {
            .podcast-title {
                color: $yellow;
            }
        }

        .podcast-title {
            color: $gray-100;
        }
    }

    .podcast-line-component {
        background-color: $gray-800;

        &.active, &:hover {
            background-color: $gray-700;
        }

        &.active {
            .title {
                color: $yellow;
            }
        }
    }

    .playlist-card-component {
        background-color: $gray-800;

        &.active, &:hover {
            background-color: $gray-700 !important;
        }

        .playlist-title {
            color: $gray-100 !important;
        }

        &.active {
            .playlist-title {
                color: $yellow !important;
            }
        }
    }

    .news-article-card-component {
        background-color: $gray-800;

        &.active, &:hover {
            background-color: $gray-700 !important;
        }

        h4 {
            color: $gray-100;
        }

        &.active {
            h4 {
                color: $yellow !important;
            }
        }

        .news-article-card-published {
            background-color: $gray-600;
            color: white;
        }
    }

    //#radio-station-playing-card
    .radio-station-card-programs {
        #radio-progression-timer {
            color: white;
        }

        .radio-station-card-component {
            background-color: $gray-800 !important;

            &:hover {
                background-color: $gray-700 !important;
            }

            .text-muted {
                color: $gray-500 !important;
            }

            span {
                color: white;
            }

            .text-truncate:not(.text-color-custom) {
                color: white ;
            }

            #radio-progress-bar-container {
                background-color: $gray-500;

                #radio-progress-bar {
                    background-color: $gray-400;
                }
            }
        }
    }

    .song-card-component {
        background-color: $gray-800;

        &.active, &:hover {
            background-color: $gray-700 !important;
        }

        &.active {
            .playlist-title {
                color: $yellow !important;
            }
            .song-title, .song-title a {
                color: $yellow !important;
            }
        }
    }

    .song-line-component {
        background-color: $gray-800 !important;

        &.active, &:hover {
            background-color: $gray-700 !important;
        }
    }

    .member-line-component {
        background-color: $gray-800 !important;

        &.active, &:hover {
            background-color: $gray-700 !important;
        }
    }

    .song-add-playlist {
        background-color: $gray-900 !important;
    }

    #programs-day-selector {
        .day {
            &:not(.active) {
                .content {
                    background-color: $gray-800;
                }
            }
        }
    }

    .programs-navigation-container {
        button.navigation-btn {
            background-color: $gray-800 !important;
            color: $gray-400 !important;
            border: none !important;
            box-shadow: none !important;
            outline: none !important;

            .icon {
                fill: $gray-400 !important;
            }

            &:focus {
                box-shadow: none !important;
                outline: none !important;
                background-color: $gray-800 !important;
            }

            &:active {
                box-shadow: none !important;
                outline: none !important;
                background-color: $gray-800 !important;
            }

            &:not(:disabled):hover {
                background-color: $secondary !important;
                color: white !important;

                .icon {
                    fill: white !important;
                }
            }
        }
    }

    .program-line-component {
        background-color: $gray-800;

        .line {
            &.active {
                .title {
                    color: $yellow;
                }
            }
        }
    }

    .event-card {
        color: $gray-100;
        background-color: $gray-800;

        &.active {
            .event-title {
                color: $yellow !important;
            }
        }

        &.active, &:hover {
            background-color: $gray-700;
        }
    }

    .progress {
        background-color: $gray-800;
    }

    // MOBILE > Player
    #player-app-mobile-header {
        background-color: $gray-900;
    }

    #nav-radio-stream-selector-dropdown {
        background-color: $gray-900;
    }

    .bg-white {
        background-color: $gray-900 !important;
    }

    .current-listeners {
        .current-listeners-text {
            color: $white;
        }
        .icon {
            fill: $white;
        }
    }

    // SWEAT ALERT 2 //////////
    .swal2-toast,
    .swal2-modal {
        background-color: $gray-800 !important;
        color: $gray-100 !important;
        border-color: $gray-100 !important;
    }
}
