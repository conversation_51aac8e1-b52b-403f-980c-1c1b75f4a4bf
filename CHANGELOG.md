# Changelog

## [2.12.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.11.0...2.12.0)

2025-09-19

* Bug activation theorical podcast page émission : #675
* Préparation SUN Electro : #676
* Playlist auto depuis Winmedia > ne pas écraser : #677
* Contact bug message "utiliser formulaire titre" : #678

## [2.11.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.10.2...2.11.0)

2025-06-23

* Fix longeur URL carto annonce : #672
* Formulaire "Nous Rejoindre" > pièces jointes de taille trop importante et bug recherche admin : #671
* Annonces > suppression auto des annonces passées : #666
* Afficher nombre ajout en titres aimés : #665
* Améliorations ajout morceaux avec Tagify : #540

## [2.10.2](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.10.1...2.10.2)

2025-04-24

* Trier rail nouveauté par release_date : #662
* Contact > option "Nous rejoindre" : #660
* Changement formulation demande SMA (page contact) 

## [2.10.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.10.0...2.10.1)

2025-04-03

* Ajout de card Emission (page lieu) : #659

## [2.10.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.9.2...2.10.0)

2025-04-02

* Adaptation bulk import podcasts pour Radio You : #658
* API Agenda : #584

## [2.9.2](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.9.1...2.9.2)

2025-03-25

* Fix Filepond bug : #657

## [2.9.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.9.0...2.9.1)

2025-03-21

* Fix : announcement image floue + XFrameOptions iframe : #656
* API Agenda > places api (search by name) : #584

## [2.9.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.8.0...2.9.0)

2025-03-20

* Bug Copy to clipboard iOS : #655
* Fix iOS corrupt data (playlists) : #653
* Label et Année dans titres en direct / Ajustements nom lieu carto / API agenda (stations et thematiques) : #652
* Optimisations page d'accueil (cache rails, index user_journeys et retrait uniqid) : #651
* Bug date event : #650
* Fix affichage répliques : #649
* Admin lieu map > récupération numéro voie, extratags et nom du lieu : #648
* Gestion des Labels : #640
* Bug Iframe > prise en compte des filtres : #632
* Indexation event pour changement location uniquement : #630


## [2.8.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.7.1...2.8.0)

2025-02-07

* Map filters update to checkbox
* Map place labels
* Lien Admin sur les pages de détails
* Condition rail grand ouest : #645
* Ordre rail performer des lieux (carte + page détail) : #646
* Voir tout "artiste" par ordre alphabétique : #647

## [2.7.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.7.0...2.7.1)

2025-01-23

* Fix touchZoomRotate

## [2.7.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.6.0...2.7.0)

2025-01-23

* Créer une page détail des lieux : #590
* Erreurs dans app/Notifications/ContactFormMessage.php : #635
* Gérer nombre de résultats recherche performer : #637
* Migration Twitter/X > Bluesky/Mastodon : #641
* Tables Admin > Nom du lieu de la localisation et liens titres/albums : #643
* Admin > Location table Program + corrections divers : #644

## [2.6.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.5.0...2.6.0)

2025-01-14

* Possibilité filtre par genre (recherche + carto) : #636
* Recherche dans les albums par le nom d'artiste : #634
* Ajustements page Agenda : #580

## [2.5.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.4.1...2.5.0)

2025-01-07

* Upgrade PHP 8.3 + Correction mimeType LibraryMediaFiles : #629

## [2.4.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.4.0...2.4.1)

2024-12-13

* Fix issues Sentry et cohérence Agenda/Actualités : #628

## [2.4.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.3.2...2.4.0)

2024-12-12

* Réagencement menus : #627
* Fix largeur max 100% pour iframes (ex: youtube) dans les description et infoPanel

## [2.3.2](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.3.1...2.3.2)

2024-12-11

* Genre sur cards artistes, pictos "en cours" au premier plan et ajout wire:key manquants : #626

## [2.3.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.3.0...2.3.1)

2024-12-10

* Suppression titre, flatpickr janvier, titre infoPanel light mode : #624
* Ajouter lieu qui fournit l'album dans la card et bandcamp url : #625

## [2.3.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.2.2...2.3.0)

2024-12-09

* Améliorations InfoPanel et page detail artistes : #622
* Changer format évènements : #619
* Configuration branche beta pour déploiement beta : #623

## [2.2.2](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.2.1...2.2.2)

2024-12-02

* Mauvais éléments affichés sur la carte et mode recherche pour partage : #621
* Grille des programmes > heure de diffusion sur mobile

## [2.2.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.2.0...2.2.1)

2024-12-02

* Affichage carto publique : #600
* Format image annonce map

## [2.2.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.1.0...2.2.0)

2024-12-02

* ElasticSearch filter program, type "Institution", fix bugs  : #613
* Admin controlleurs > désactivation bouton lors de la soumission : #614
* Outil de fusion des lieux, localisation stations et corrections diverses (iframe, option tinymce, tag performer detail) : #615
* Afficher toutes les stations localisées ensemble sur la carte : #617
* Option station pour partage et correctifs divers : #618
* Bug affichage évènement dates : #620
* Possibilité d'importer des GIF dans les descriptions : #574
* Mise en avant Carto dans les annonces

## [2.1.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.0.3...2.1.0)

2024-11-22

* Gestion en admin des durées pour la carto  : #604
* Corrections performers (après import des musiques archivage)  : #608
* Correction id/imedia playlist podcast : #609
* Permettre la configuration d'une localisation depuis une émission : #610
* Affichage point rouge évènement sur plusieurs jours : #611
* Ajout type de lieu "studio d'enregistrement / podcast" : #612

## [2.0.3](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.0.2...2.0.3)

2024-11-20

* Image album si pas d'image artiste : #606
* Filtres artistes/albums : #607

## [2.0.2](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.0.1...2.0.2)

2024-11-18

* Finitions pour import archivage : #605

## [2.0.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/2.0.0...2.0.1)

2024-11-14

* Ajustements tables admin location : #602
* Ajustements date et ordre évènements : #603

## [2.0.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.26.0...2.0.0)

2024-11-13

* CARTO (Makina) : https://gitlab.lesonunique.com/**********************/lesonunique.com/-/milestones/21 · https://gitlab.lesonunique.com/**********************/lesonunique.com/-/milestones/22 · https://gitlab.lesonunique.com/**********************/lesonunique.com/-/milestones/23 · https://gitlab.lesonunique.com/**********************/lesonunique.com/-/milestones/24 · https://gitlab.lesonunique.com/**********************/lesonunique.com/-/milestones/25
* Initialisation stucture archivage : #548
* Edition admin des musiques : #557
* Complément de data pour les artiste : #558
* Edition des images (artistes, pochettes) et nom albums/artistes : #571
* Import données OSM / OpenData : #586
* Correctifs suite archivage : #591
* Correctifs dedicace : #592
* Admin geocoder > finalisations : #593
* Scroll top InfoPanel : #595
* Rail map > affichage nom du lieu sur mobile : #596
* Masquer carto pour mise en prod "beta" interne : #598
* Migration PostgreSQL : #499
* Erreur affichage évènement non activé : #539
* Suivi et correction bug Tagify : #541
* Formulaire > modifications : #562
* Capture verifyUserEligibility error timeout and 500 : #578
* Amélioration recherche contenus admin : #579
* Images corps article, podcast, … : #582
* Edit robots.txt : #594
* Ordonner browse events en fonction de la date (sans l'heure) : #601

## [1.26.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.25.4...1.26.0)

2024-07-25

* Nouveaux formulaires avec FilePond : #402
* Program Reccurence > Bug tag "Dimanche" qui disparait : #552
* Bug notify WelcomeController : #550

## [1.25.4](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.25.3...1.25.4)

2024-07-01

* Fix temporaire taille max fichier page contact : #549

## [1.25.3](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.25.2...1.25.3)

2024-06-20

* Bug affichage Tagify : #538
* TinyMCE > bug lists : #537

## [1.25.2](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.25.1...1.25.2)

2024-06-17

* Page musiques > masquer avatars dédicaces : #531
* Création de compte > augmenter durée de validité lien (2h) : #530
* Admin > Bouton création de compte manquant : #529
* Bug Elasticsearch : #518

## [1.25.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.25.0...1.25.1)

2024-04-17

* Augmenter limite d'envoi "Nous rejoindre" : #498

## [1.25.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.24.3...1.25.0)

2024-04-16

* Evolution "nous rejoindre" : #497

## [1.24.3](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.24.2...1.24.3)

2024-04-09

* Iframe > bug scroll global (correctif) : #491

## [1.24.2](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.24.1...1.24.2)

2024-04-09

* Correction ratio events : #495
* Iframe > bug scroll global : #491

## [1.24.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.24.0...1.24.1)

2024-03-27

* Corrections accès par url du service dedicace : #492

## [1.24.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.23.1...1.24.0)

2024-03-25

* Mise à jour Laravel 10 et ElasticSearch 8 : #380
* Fix déplacement du scroll des rails (livewire refresh) : #489
* Rail ToInspireYou > Bouton dedicace en étant non connecté : #490

## [1.23.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.23.0...1.23.1)

2024-02-15

* Afficher bouton dédicace grisé pour utilisateur ineligible : #484
* Admin Podcasts > boolean audio present : #483
* Validation dédicace > bug refresh view : #482
* Admin > Menus sélection radio obligatoire et podcast Publication ON/OFF : #479

## [1.23.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.22.6...1.23.0)

2024-02-13

* DEDICACE V2 : #125
* Migration Okipa/laravel-table vers rappasoft/laravel-livewire-tables : #477
* Admin > PublicPlaylistsTable > Afficher uniquement playlists publiques : #480

## [1.22.6](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.22.5...1.22.6)

2023-12-21

* Scheduler commands that should run at night : #476

## [1.22.5](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.22.4...1.22.5)

2023-12-15

* Flux RSS category default "Actu" : #475

## [1.22.4](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.22.3...1.22.4)

2023-12-15

* Flux RSS articles et évènements : #474

## [1.22.3](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.22.2...1.22.3)

2023-12-14

* RefreshSongsIndex everyFourMinutes instead everyFiveMinutes : #473

## [1.22.2](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.22.1...1.22.2)

2023-12-14

* Bug script PerformersAlbums sync ES : #471
* Page contact et candidature > différencier objet mail : #470

## [1.22.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.22.0...1.22.1)

2023-12-08

* Ajouter la possibilité de générer plusieurs podcasts en avance : #434
* Ordre affichage évènements : #462
* Correction ratio card articles : #469

## [1.22.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.21.0...1.22.0)

2023-12-01

* Accès direct univers : #443
* Bug lecture sur annonces des podcasts : #466
* Bug Klaro delete Google Analytics cookies : #467
* Mise à la norme W3C des feeds RSS : #468

## [1.21.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.20.1...1.21.0)

2023-11-22

* Ajouter lectures sur cards articles, évènements et annonces : #452
* Lectures sur annonces également pour émission : #465
* Mise en place suivi Google Analytics : #463
* Fix songs pluck id -> imedia : #461

## [1.20.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.20.0...1.20.1)

2023-11-07

* Créer des pages pour les playlists et émissions : #414

## [1.20.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.19.0...1.20.0)

2023-11-06

* Ajouter limite traitement refresh songs : #458
* Fix album name truncate : #457
* Ajout partage iframe article : #456
* Remplacer "Dédicace ... par un utilisateur" => "Dédicace ... par un auditeur" : #455
* Dédicaces > ordre affichage recherche : #454
* Fix thumbnail rail playlist recherche : #453
* Fix iframe border : #451
* Fix iframe émission ordre podcasts : #450
* Ajouter option tri dans BulkImportPodcasts : #448

## [1.19.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.18.0...1.19.0)

2023-09-29

* Widget -> Partage iframe des contenus : #419

## [1.18.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.17.0...1.18.0)

2023-09-25

* Améliorations playlist : #446
* Mémoriser les filtres, recherches et pages des tables d'admin (+ editor accès annonces) : #445
* Passer visuel playlist en format carré : #444
* Changer logo twitter (x) et corriger affichage & (&amp;) : #442
* Centrer la cover player mobile : #441

## [1.17.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.16.0...1.17.0)

2023-09-01

* Associer/dissocier sous-programmes, fix heure récurrences et nettoyage config acid : #440
* Filtre des podcasts par station : #439
* Fix couleur auteur podcast : #438

## [1.16.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.15.1...1.16.0)

2023-08-28

* Intégration > Dark mode : #163
* Fix album réplique et visuels rail to-inspire-you : #436
* Fix format visuels répliques : #437

## [1.15.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.15.0...1.15.1)

2023-07-12

* Liens entre les contenus > Fix suggestedEvents null : #435

## [1.15.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.14.0...1.15.0)

2023-07-05

* Liens entre les contenus : #361
* Problèmes d'encodage : #433

## [1.14.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.13.0...1.14.0)

2023-06-23

* Admin > Evenements > Recherche lieu via API : #194
* Admin > Localisation API > Champ label localisation : #431
* Affichage extraits répliques films sur plateforme : #332
* Import groupé de podcasts : #418
* Bug réglage vacances ALL : #421
* Ajouter description playlists : #422
* Ajouter m3u SUN Ciné & Séries : #423
* Afficher artistes, musiques et playlists avec le filtre par Tag : #426
* Masquer rails artiste si vide : #427
* Bug indexation podcast : #428
* Ajout activation rapide Annonces : #429
* Annonces > ajustements administration : #432
* Dedicace > afficher résultats selon ordre retourné par le service : #430

## [1.13.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.12.0...1.13.0)

2023-05-25

* Vérification Email : #417
* Ajouter fichier ads.txt : #416
* Add program ID in matomo stats : #415

## [1.12.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.11.4...1.12.0)

2023-05-10

* Évolution mécanique et affichage FM/DAB : #365
* Nouvelle stations Angers / RSY : #401
* Amélioration recherche ElasticSearch Podcasts : #406
* Fix mosaicCover Playlists : #407
* Sélection webradio préférée : #408
* Localisation > si distance supérieure à 70km -> station par défaut : #409
* Matomo > gestion des utilisateurs refusant les cookies : #410
* Admin > Ordre affichage stations : #411
* Ajout menu formations pro et fix LazyLoad podcast.program : #413

## [1.11.4](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.11.3...1.11.4)

2023-04-20

* Update dependencies and track "canceled" and "error" in Dedicaces : #405

## [1.11.3](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.11.2...1.11.3)

2023-04-19

* Tracking Dedicaces : #404

## [1.11.2](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.11.1...1.11.2)

2023-04-18

* Matomo -> Configuration Media Analytics : #322

## [1.11.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.11.0...1.11.1)

2023-04-13

* Fix Undefined array key "song_performer_id" : #403

## [1.11.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.10.0...1.11.0)

2023-04-13

* Retirer middleware ForgotOldSessionCookies : #400
* Detail Podcast > Afficher plus de podcasts précédents : #398
* Liens artistes/albums/emission sur l'ensemble de la plateforme : #393
* Commande mise à jour auteurs podcasts (correctif) : #390

## [1.10.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.9.2...1.10.0)

2023-03-08

* Commande mise à jour auteurs podcasts : #390
* Annonces > ajout podcasts : #389
* Badges et tags > clic et recherche filtrée : #382

## [1.9.2](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.9.1...1.9.2)

2023-02-23

* Musique > Affichage "version" (correctif modale dédicace) : #383

## [1.9.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.9.0...1.9.1)

2023-02-23

* Fix lost remember login : #388
* Fix playing status null (correctif) : #384

## [1.9.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.8.2...1.9.0)

2023-02-22

* Player > Réglage volume (Desktop) : #311
* Installation et configuration Laravel Sanctum : #331
* Notification Media > Informations en cours de lecture : #348
* Rails nouveautés > bug filtre par station : #372
* Web home > Fix Livewire undefined (error console) : #373
* Ouvrir tableau de bord dans un nouvel onglet : #374
* Bug année musique au mauvais format : #375
* Auteurs contenus : #377
* Amélioration recherche par nom du programme (podcasts) : #378
* Musique > Affichage "version" : #383
* Fix playing status null : #384
* Add GIF and WEBP in LibraryMediaFile mimetypes : #385
* Fix Image source not readable : #386
* Update dependencies and remove "fruitcake/laravel-cors" (included in "laravel/framework" > 9.2)  : #387

## [1.8.2](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.8.1...1.8.2)

2023-01-26

* Changer couleur liens pages home en bleu (correctif) : #369
* Déplier par défaut la grille des programmes : #371

## [1.8.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.8.0...1.8.1)

2023-01-26

* Retirer autofocus formulaires : #368
* Changer couleur liens pages home en bleu : #369
* Ajout possibilité édition description des annonces : #370

## [1.8.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.7.0...1.8.0)

2023-01-25

* Page Album > Détail : #346
* Rails Musique : #347
* Corrections composants musiques : #362
* Fix bug suite MAJ Laravel 9 (avec Table Livewire) : #355
* Taille player admin : #358
* Retrait patchs Livewire (bug iOS) : #356

## [1.7.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.6.1...1.7.0)

2022-12-16

* Mise à jour Laravel 8 -> 9 : #350
* NewsArticle > Remove conversion "cover" from collection "illustrations" : #353
* Stations > réorganisation position : #354

## [1.6.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.6.0...1.6.1)

2022-12-05

* Retirer fenêtre de connexion en arrivant sur le site : #345

## [1.6.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.5.0...1.6.0)

2022-11-30

* iOS fix HLS + retrait audio_full mp3 : #343
* Mobile > Ajout avatar player fullscreen + fav icon footer : #342
* Correction textes/liens : #341
* Recherche > Amélioration Elasticsearch : #324
* En direct sur SUN > Afficher émission si plus de musique : #224

## [1.5.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.4.3...1.5.0)

2022-11-23

* Bug iOS : #336
* Player > Bouton suivant/précédent sur un morceau : #235
* Profile > Multiples avatars aléatoires par défaut : #338
* Corrections affichage auteur dédicace : #339
* Masquer téléchargements app dans PWA & Fix "performer_name" recherche (DedicationSuggestion + SongResults) (correctif) : #337
* Fix artiste contenus relatifs avec double quote : #340

## [1.4.3](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.4.2...1.4.3)

2022-11-14

* Masquer téléchargements app dans PWA & Fix "performer_name" recherche (DedicationSuggestion + SongResults) : #337

## [1.4.2](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.4.1...1.4.2)

2022-11-10

* Corrections fichier manifest pour déploiement nouvelle version appli : #335

## [1.4.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.4.0...1.4.1)

2022-11-09

* Liens m3u des stations manquants dans dossier /public : #333
* Podcast controller > Fix bug durée podcast 1s : #334

## [1.4.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.3.8...1.4.0)

2022-11-03

* Réorganisation des menus et accès direct mySUN en étant connecté : #230
* Amélioration comportement bouton retour et historique : #282
* Musiques > Ajustement informations complémentaires : #315
* Fix récurrence "dernière semaine du mois" : #328
* Fix search rails (performers & playlists) : #329
* Ajout modifications manquantes dans controlleurs admin : #330
* Recherche > Amélioration Elasticsearch >> Indexation champs manquants : #325

## [1.3.8](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.3.7...1.3.8)

2022-10-25

* Corrections référencement : #323
* tinyMCE add option paste_as_text default

## [1.3.7](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.3.6...1.3.7)

2022-10-20

* Matomo > Améliorations SPA : #129

## [1.3.6](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.3.5...1.3.6)

2022-10-19

* Analyse Lighthouse > Correctifs : #318

## [1.3.5](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.3.4...1.3.5)

2022-10-14

* Erreur 500 "aléatoire" en arrivant sur le site : #313
* Ajout lien flux RSS sur les pages détail émissions et podcasts : #316
* Fix navbar home : #317

## [1.3.4](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.3.3...1.3.4)

2022-10-13

* Podcasts > Ne pas afficher avant date de publication : #314

## [1.3.3](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.3.2...1.3.3)

2022-10-13

* Boutons partage (événements, actus, podcasts) > Problème partage FB/Twitter/Linkedin : #308

## [1.3.2](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.3.1...1.3.2)

2022-10-12

* Erreur 500 "aléatoire" en arrivant sur le site : #313
* Boutons partage (événements, actus, podcasts) > Problème partage FB/Twitter/Linkedin : #308

## [1.3.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.3.0...1.3.1)

2022-10-05

* Playlist publiques > morceaux pouvant être supprimés par un auditeur : #312
* Announcements > Call to a member function img() on null : #310

## [1.3.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.2.1...1.3.0)

2022-09-30

* Boutons partage (événements, actus, podcasts) > Problème partage FB/Twitter/Linkedin : #308

## [1.2.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.2.0...1.2.1)

2022-09-29

* Fix des droits d'accès recherche playlist admin : #307

## [1.2.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.1.1...1.2.0)

2022-09-29

* Header > Ajout d'un compteur d'auditeur en live : #157
* Correction des droits d'accès interface admin : #284
* Details émission > ajustement patchwork visuels : #302
* Fix z-index bouton dedicace mobile : #303
* Front > Programmes > Affichage durée podcast au lieu de l'émission : #304
* Admin > Podcasts > Afficher uniquement "date de publication" (sauf pour Admin) : #305
* Home Web > Ajustement briques (correctif) : #192
* RSS > Un flux par émission (correctif) : #234
* Recherche ElasticSearch > Podcasts > Ajustements (correctif) : #259
* Admin > Outil d'édition de texte (correctif) : #260
* Admin > Gestion émission > Afficher valeur en minutes (correctif) : #232

## [1.1.1](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.1.0...1.1.1)

2022-09-23

* Problème barre progression : #301

## [1.1.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/compare/1.0.0...1.1.0)

2022-09-23

* Migration stockage parcours utilisateur : Session => BDD : #300
* Corrections profil utilisateur : #299
* Page détail artiste (correctif) : #296
* Front > Recherche > Artistes (correctif) : #266
* Backend > Winmedia > Extraction Artistes/Albums (correctif) : #264
* Admin > Outil d'édition de texte : #260
* Formulaires de contact (correctif) : #249
* Player > Barre de défilement à synchroniser avec "En direct" (correctif) : #236
* RSS > Un flux par émission (correctif) : #234
* Desktop > Radio > Dropdown flux radio (correctif) : #223
* Admin > Playlists publiques > Gestion (correctif) : #147

## [1.0.0](https://gitlab.lesonunique.com/**********************/lesonunique.com/-/tags/1.0.0)

2022-09-05

First release
